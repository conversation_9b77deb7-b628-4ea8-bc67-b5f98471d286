[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "35", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "36", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "37", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "38"}, {"size": 550, "mtime": 1751002257124, "results": "39", "hashOfConfig": "40"}, {"size": 362, "mtime": 1751001516282, "results": "41", "hashOfConfig": "40"}, {"size": 2315, "mtime": 1751014584116, "results": "42", "hashOfConfig": "40"}, {"size": 5912, "mtime": 1751002889593, "results": "43", "hashOfConfig": "40"}, {"size": 9398, "mtime": 1751013933072, "results": "44", "hashOfConfig": "40"}, {"size": 6144, "mtime": 1751003013744, "results": "45", "hashOfConfig": "40"}, {"size": 16590, "mtime": 1751017484206, "results": "46", "hashOfConfig": "40"}, {"size": 8045, "mtime": 1751003816943, "results": "47", "hashOfConfig": "40"}, {"size": 13480, "mtime": 1751003649429, "results": "48", "hashOfConfig": "40"}, {"size": 26580, "mtime": 1751008212115, "results": "49", "hashOfConfig": "40"}, {"size": 8562, "mtime": 1751007249570, "results": "50", "hashOfConfig": "40"}, {"size": 6483, "mtime": 1751001825178, "results": "51", "hashOfConfig": "40"}, {"size": 665, "mtime": 1751001779724, "results": "52", "hashOfConfig": "40"}, {"size": 10403, "mtime": 1751017441838, "results": "53", "hashOfConfig": "40"}, {"size": 3887, "mtime": 1751019446562, "results": "54", "hashOfConfig": "40"}, {"size": 9454, "mtime": 1751013836375, "results": "55", "hashOfConfig": "40"}, {"size": 6079, "mtime": 1751003779872, "results": "56", "hashOfConfig": "40"}, {"size": 4406, "mtime": 1751003274192, "results": "57", "hashOfConfig": "40"}, {"size": 10667, "mtime": 1751006877684, "results": "58", "hashOfConfig": "40"}, {"size": 5107, "mtime": 1751003753867, "results": "59", "hashOfConfig": "40"}, {"size": 7270, "mtime": 1751002967950, "results": "60", "hashOfConfig": "40"}, {"size": 7956, "mtime": 1751007184267, "results": "61", "hashOfConfig": "40"}, {"size": 11510, "mtime": 1751009626258, "results": "62", "hashOfConfig": "40"}, {"size": 12558, "mtime": 1751004256417, "results": "63", "hashOfConfig": "40"}, {"size": 10775, "mtime": 1751004342324, "results": "64", "hashOfConfig": "40"}, {"size": 12479, "mtime": 1751004391960, "results": "65", "hashOfConfig": "40"}, {"size": 15195, "mtime": 1751005588991, "results": "66", "hashOfConfig": "40"}, {"size": 13401, "mtime": 1751003915007, "results": "67", "hashOfConfig": "40"}, {"size": 10910, "mtime": 1751003957303, "results": "68", "hashOfConfig": "40"}, {"size": 3964, "mtime": 1751003442458, "results": "69", "hashOfConfig": "40"}, {"size": 8498, "mtime": 1751003577995, "results": "70", "hashOfConfig": "40"}, {"size": 9054, "mtime": 1751003411814, "results": "71", "hashOfConfig": "40"}, {"size": 11145, "mtime": 1751009654679, "results": "72", "hashOfConfig": "40"}, {"size": 12716, "mtime": 1751002934527, "results": "73", "hashOfConfig": "40"}, {"size": 5054, "mtime": 1751005576405, "results": "74", "hashOfConfig": "40"}, {"size": 7154, "mtime": 1751017196047, "results": "75", "hashOfConfig": "40"}, {"size": 5162, "mtime": 1751003518513, "results": "76", "hashOfConfig": "40"}, {"size": 11914, "mtime": 1751004034012, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["192"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["193"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", ["194", "195", "196", "197", "198"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["199", "200", "201", "202", "203", "204", "205", "206"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["207", "208", "209", "210"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["211", "212", "213"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx", ["214", "215", "216"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["217"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts", ["218", "219"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["220", "221"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["222", "223"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["224", "225", "226", "227", "228", "229"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["230", "231"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["232", "233", "234"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["235"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["236"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx", ["237", "238"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["239", "240"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["241"], [], {"ruleId": "242", "severity": 1, "message": "243", "line": 17, "column": 7, "nodeType": "244", "messageId": "245", "endLine": 17, "endColumn": 62}, {"ruleId": "242", "severity": 1, "message": "246", "line": 16, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 16, "endColumn": 8}, {"ruleId": "242", "severity": 1, "message": "247", "line": 6, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "248", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 15}, {"ruleId": "242", "severity": 1, "message": "249", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 12}, {"ruleId": "242", "severity": 1, "message": "250", "line": 14, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "251", "line": 15, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "252", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 7}, {"ruleId": "242", "severity": 1, "message": "253", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "254", "line": 12, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 10}, {"ruleId": "242", "severity": 1, "message": "255", "line": 13, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "256", "line": 14, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 7}, {"ruleId": "242", "severity": 1, "message": "257", "line": 15, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 8}, {"ruleId": "242", "severity": 1, "message": "258", "line": 21, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 21, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "259", "line": 21, "column": 19, "nodeType": "244", "messageId": "245", "endLine": 21, "endColumn": 26}, {"ruleId": "242", "severity": 1, "message": "260", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "261", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 6}, {"ruleId": "242", "severity": 1, "message": "262", "line": 14, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 4}, {"ruleId": "242", "severity": 1, "message": "263", "line": 15, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 7}, {"ruleId": "242", "severity": 1, "message": "247", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "264", "line": 26, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 26, "endColumn": 13}, {"ruleId": "265", "severity": 1, "message": "266", "line": 57, "column": 6, "nodeType": "267", "endLine": 57, "endColumn": 22, "suggestions": "268"}, {"ruleId": "242", "severity": 1, "message": "269", "line": 6, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 14}, {"ruleId": "242", "severity": 1, "message": "270", "line": 7, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 7, "endColumn": 14}, {"ruleId": "265", "severity": 1, "message": "271", "line": 51, "column": 6, "nodeType": "267", "endLine": 51, "endColumn": 27, "suggestions": "272"}, {"ruleId": "242", "severity": 1, "message": "273", "line": 8, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 8, "endColumn": 23}, {"ruleId": "242", "severity": 1, "message": "259", "line": 12, "column": 23, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 30}, {"ruleId": "265", "severity": 1, "message": "274", "line": 300, "column": 6, "nodeType": "267", "endLine": 300, "endColumn": 8, "suggestions": "275"}, {"ruleId": "242", "severity": 1, "message": "276", "line": 5, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 10}, {"ruleId": "242", "severity": 1, "message": "246", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 8}, {"ruleId": "242", "severity": 1, "message": "276", "line": 5, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 10}, {"ruleId": "242", "severity": 1, "message": "246", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 8}, {"ruleId": "242", "severity": 1, "message": "277", "line": 1, "column": 20, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 29}, {"ruleId": "242", "severity": 1, "message": "278", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "279", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 8}, {"ruleId": "242", "severity": 1, "message": "259", "line": 13, "column": 23, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 30}, {"ruleId": "242", "severity": 1, "message": "258", "line": 13, "column": 32, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 39}, {"ruleId": "242", "severity": 1, "message": "252", "line": 13, "column": 41, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 45}, {"ruleId": "242", "severity": 1, "message": "280", "line": 3, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 3, "endColumn": 31}, {"ruleId": "265", "severity": 1, "message": "281", "line": 87, "column": 6, "nodeType": "267", "endLine": 87, "endColumn": 8, "suggestions": "282"}, {"ruleId": "242", "severity": 1, "message": "252", "line": 13, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 14}, {"ruleId": "242", "severity": 1, "message": "258", "line": 13, "column": 16, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 23}, {"ruleId": "242", "severity": 1, "message": "259", "line": 13, "column": 25, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 32}, {"ruleId": "242", "severity": 1, "message": "260", "line": 5, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "261", "line": 2, "column": 25, "nodeType": "244", "messageId": "245", "endLine": 2, "endColumn": 28}, {"ruleId": "242", "severity": 1, "message": "259", "line": 12, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 17}, {"ruleId": "265", "severity": 1, "message": "283", "line": 45, "column": 6, "nodeType": "267", "endLine": 45, "endColumn": 16, "suggestions": "284"}, {"ruleId": "242", "severity": 1, "message": "255", "line": 8, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 8, "endColumn": 9}, {"ruleId": "265", "severity": 1, "message": "285", "line": 34, "column": 6, "nodeType": "267", "endLine": 34, "endColumn": 8, "suggestions": "286"}, {"ruleId": "242", "severity": 1, "message": "287", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'Filter' is defined but never used.", "'TrendingDown' is defined but never used.", "'BarChart3' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'Bar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadReport'. Either include it or remove the dependency array.", "ArrayExpression", ["288"], "'AlertCircle' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'syncOfflineData'. Either include it or remove the dependency array.", ["289"], "'persistentLocalCache' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["290"], "'getDocs' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["291"], "React Hook useEffect has missing dependencies: 'calculateMetrics' and 'generateAlerts'. Either include them or remove the dependency array.", ["292"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["293"], "'Printer' is defined but never used.", {"desc": "294", "fix": "295"}, {"desc": "296", "fix": "297"}, {"desc": "298", "fix": "299"}, {"desc": "300", "fix": "301"}, {"desc": "302", "fix": "303"}, {"desc": "304", "fix": "305"}, "Update the dependencies array to be: [loadReport, selectedPeriod]", {"range": "306", "text": "307"}, "Update the dependencies array to be: [offlineQueue.length, syncOfflineData]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "310", "text": "311"}, "Update the dependencies array to be: [removeFromCart]", {"range": "312", "text": "313"}, "Update the dependencies array to be: [calculateMetrics, generateAlerts, products]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [loadUsers]", {"range": "316", "text": "317"}, [1381, 1397], "[load<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>]", [1357, 1378], "[offlineQueue.length, syncOfflineData]", [9246, 9248], "[loadDashboardData]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]", [1163, 1173], "[calculateMetrics, generateAlerts, products]", [865, 867], "[loadUsers]"]